import express from 'express';
import { body, validationResult } from 'express-validator';
import multer from 'multer';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import prisma from '../config/database.js';
import { authenticateToken, requireRole, requireOwnershipOrRole } from '../middleware/auth.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, path.join(__dirname, '../uploads/receipts'));
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'receipt-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 5 * 1024 * 1024 // 5MB
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|pdf/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('يُسمح فقط بملفات الصور (JPEG, PNG) وملفات PDF'));
    }
  }
});

// Get all payments
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 10, shipmentId, customerId } = req.query;
    const skip = (page - 1) * limit;

    let where = {};

    // Filter by customer for CUSTOMER role
    if (req.user.role === 'CUSTOMER') {
      const customer = await prisma.customer.findUnique({
        where: { userId: req.user.id }
      });
      if (!customer) {
        return res.status(404).json({
          success: false,
          error: 'بيانات العميل غير موجودة',
          message: 'Customer profile not found'
        });
      }
      where.shipment = { customerId: customer.id };
    }

    // Add filters
    if (shipmentId) {
      where.shipmentId = shipmentId;
    }
    if (customerId && ['ADMIN', 'EMPLOYEE', 'ACCOUNTANT'].includes(req.user.role)) {
      where.shipment = { customerId };
    }

    const [payments, total] = await Promise.all([
      prisma.payment.findMany({
        where,
        include: {
          shipment: {
            select: {
              id: true,
              trackingNumber: true,
              totalAmount: true,
              customer: {
                select: {
                  id: true,
                  fullName: true,
                  companyName: true
                }
              }
            }
          },
          processedBy: {
            select: {
              id: true,
              fullName: true
            }
          }
        },
        skip: parseInt(skip),
        take: parseInt(limit),
        orderBy: { paymentDate: 'desc' }
      }),
      prisma.payment.count({ where })
    ]);

    res.json({
      success: true,
      data: {
        payments,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          totalPages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('Get payments error:', error);
    res.status(500).json({
      success: false,
      error: 'خطأ في الخادم',
      message: 'Internal server error'
    });
  }
});

// Get payment by ID
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const payment = await prisma.payment.findUnique({
      where: { id },
      include: {
        shipment: {
          include: {
            customer: {
              select: {
                id: true,
                fullName: true,
                phone: true,
                companyName: true
              }
            }
          }
        },
        processedBy: {
          select: {
            id: true,
            fullName: true
          }
        }
      }
    });

    if (!payment) {
      return res.status(404).json({
        success: false,
        error: 'المدفوعة غير موجودة',
        message: 'Payment not found'
      });
    }

    // Check if customer is accessing their own payment
    if (req.user.role === 'CUSTOMER') {
      const customer = await prisma.customer.findUnique({
        where: { userId: req.user.id }
      });
      if (!customer || payment.shipment.customerId !== customer.id) {
        return res.status(403).json({
          success: false,
          error: 'ليس لديك صلاحية للوصول إلى هذا المورد',
          message: 'Access denied'
        });
      }
    }

    res.json({
      success: true,
      data: payment
    });

  } catch (error) {
    console.error('Get payment error:', error);
    res.status(500).json({
      success: false,
      error: 'خطأ في الخادم',
      message: 'Internal server error'
    });
  }
});

// Create new payment (Accountant/Admin only)
router.post('/', [
  authenticateToken,
  requireRole(['ADMIN', 'ACCOUNTANT']),
  body('shipmentId').notEmpty().withMessage('معرف الشحنة مطلوب'),
  body('amount').isFloat({ min: 0.01 }).withMessage('المبلغ يجب أن يكون أكبر من 0'),
  body('paymentMethod').isIn(['CASH', 'BANK_TRANSFER', 'CREDIT_CARD', 'CHECK', 'ONLINE', 'OTHER'])
    .withMessage('طريقة الدفع غير صحيحة')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'بيانات غير صحيحة',
        message: 'Validation failed',
        details: errors.array()
      });
    }

    const {
      shipmentId,
      amount,
      currency = 'USD',
      paymentMethod,
      paymentDate = new Date(),
      receiptNumber,
      notes
    } = req.body;

    // Verify shipment exists
    const shipment = await prisma.shipment.findUnique({
      where: { id: shipmentId }
    });

    if (!shipment) {
      return res.status(404).json({
        success: false,
        error: 'الشحنة غير موجودة',
        message: 'Shipment not found'
      });
    }

    // Check if payment amount doesn't exceed remaining amount
    if (parseFloat(amount) > shipment.remainingAmount) {
      return res.status(400).json({
        success: false,
        error: 'المبلغ المدفوع يتجاوز المبلغ المتبقي',
        message: 'Payment amount exceeds remaining amount'
      });
    }

    // Get accountant profile
    const accountant = await prisma.accountant.findUnique({
      where: { userId: req.user.id }
    });

    // Create payment
    const payment = await prisma.payment.create({
      data: {
        shipmentId,
        amount: parseFloat(amount),
        currency,
        paymentMethod,
        paymentDate: new Date(paymentDate),
        receiptNumber,
        notes,
        processedById: accountant?.id
      },
      include: {
        shipment: {
          include: {
            customer: {
              select: {
                id: true,
                fullName: true
              }
            }
          }
        }
      }
    });

    // Update shipment amounts
    const newPaidAmount = shipment.paidAmount + parseFloat(amount);
    const newRemainingAmount = shipment.totalAmount - newPaidAmount;

    await prisma.shipment.update({
      where: { id: shipmentId },
      data: {
        paidAmount: newPaidAmount,
        remainingAmount: newRemainingAmount
      }
    });

    // Create notification for customer
    const customer = await prisma.customer.findUnique({
      where: { id: payment.shipment.customerId },
      include: { user: true }
    });

    if (customer?.user) {
      await prisma.notification.create({
        data: {
          userId: customer.user.id,
          title: 'تم تسجيل دفعة جديدة',
          message: `تم تسجيل دفعة بمبلغ ${amount} ${currency} للشحنة ${shipment.trackingNumber}`,
          type: 'PAYMENT'
        }
      });
    }

    res.status(201).json({
      success: true,
      message: 'تم تسجيل الدفعة بنجاح',
      data: payment
    });

  } catch (error) {
    console.error('Create payment error:', error);
    res.status(500).json({
      success: false,
      error: 'خطأ في الخادم',
      message: 'Internal server error'
    });
  }
});

// Upload payment receipt (Customer can upload, Accountant can update)
router.post('/:id/receipt', [
  authenticateToken,
  upload.single('receipt')
], async (req, res) => {
  try {
    const { id } = req.params;

    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'ملف الإيصال مطلوب',
        message: 'Receipt file is required'
      });
    }

    const payment = await prisma.payment.findUnique({
      where: { id },
      include: {
        shipment: {
          include: {
            customer: true
          }
        }
      }
    });

    if (!payment) {
      return res.status(404).json({
        success: false,
        error: 'المدفوعة غير موجودة',
        message: 'Payment not found'
      });
    }

    // Check permissions
    if (req.user.role === 'CUSTOMER') {
      const customer = await prisma.customer.findUnique({
        where: { userId: req.user.id }
      });
      if (!customer || payment.shipment.customerId !== customer.id) {
        return res.status(403).json({
          success: false,
          error: 'ليس لديك صلاحية لتعديل هذا المورد',
          message: 'Access denied'
        });
      }
    } else if (!['ADMIN', 'ACCOUNTANT'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        error: 'ليس لديك صلاحية لتعديل هذا المورد',
        message: 'Access denied'
      });
    }

    // Update payment with receipt file
    const updatedPayment = await prisma.payment.update({
      where: { id },
      data: {
        receiptFile: req.file.filename
      }
    });

    res.json({
      success: true,
      message: 'تم رفع إيصال الدفع بنجاح',
      data: {
        receiptFile: req.file.filename,
        receiptUrl: `/uploads/receipts/${req.file.filename}`
      }
    });

  } catch (error) {
    console.error('Upload receipt error:', error);
    res.status(500).json({
      success: false,
      error: 'خطأ في الخادم',
      message: 'Internal server error'
    });
  }
});

export default router;
