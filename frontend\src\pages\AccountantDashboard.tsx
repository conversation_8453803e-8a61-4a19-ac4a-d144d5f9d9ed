import { Routes, Route } from 'react-router-dom'
import { CreditCardIcon, DocumentTextIcon, BanknotesIcon, ChartBarIcon } from '@heroicons/react/24/outline'

const AccountantDashboard = () => {
  return (
    <Routes>
      <Route path="/" element={<AccountantHome />} />
      <Route path="/payments" element={<AccountantPayments />} />
      <Route path="/reports" element={<AccountantReports />} />
      <Route path="/shipments" element={<AccountantShipments />} />
    </Routes>
  )
}

const AccountantHome = () => {
  const stats = [
    { name: 'إجمالي الإيرادات', value: '$45,231', icon: BanknotesIcon, color: 'bg-green-500' },
    { name: 'المدفوعات اليوم', value: '12', icon: CreditCardIcon, color: 'bg-blue-500' },
    { name: 'المدفوعات المعلقة', value: '8', icon: DocumentTextIcon, color: 'bg-yellow-500' },
    { name: 'التقارير الشهرية', value: '3', icon: ChartBarIcon, color: 'bg-purple-500' },
  ]

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">لوحة المحاسب</h1>
        <p className="mt-1 text-sm text-gray-600">
          إدارة المدفوعات والتقارير المالية
        </p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <div key={stat.name} className="card">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className={`${stat.color} rounded-md p-3`}>
                  <stat.icon className="h-6 w-6 text-white" />
                </div>
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    {stat.name}
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {stat.value}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Recent Payments */}
      <div className="card">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            المدفوعات الأخيرة
          </h3>
          <div className="text-center py-8 text-gray-500">
            لا توجد مدفوعات حالياً
          </div>
        </div>
      </div>
    </div>
  )
}

const AccountantPayments = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">المدفوعات</h1>
          <p className="mt-1 text-sm text-gray-600">
            إدارة جميع المدفوعات والإيصالات
          </p>
        </div>
        <button className="btn-primary">
          <CreditCardIcon className="h-5 w-5 ml-2" />
          تسجيل دفعة جديدة
        </button>
      </div>

      <div className="card">
        <div className="px-4 py-5 sm:p-6">
          <div className="text-center py-8 text-gray-500">
            لا توجد مدفوعات حالياً
          </div>
        </div>
      </div>
    </div>
  )
}

const AccountantReports = () => {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">التقارير المالية</h1>
        <p className="mt-1 text-sm text-gray-600">
          إنشاء وتصدير التقارير المالية
        </p>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <div className="card">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              تقرير الإيرادات الشهرية
            </h3>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">من تاريخ</span>
                <input type="date" className="form-input w-40" />
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">إلى تاريخ</span>
                <input type="date" className="form-input w-40" />
              </div>
              <div className="flex gap-2">
                <button className="btn-primary flex-1">عرض التقرير</button>
                <button className="btn-secondary">تصدير PDF</button>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              تقرير المدفوعات حسب العميل
            </h3>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">العميل</span>
                <select className="form-input w-40">
                  <option>جميع العملاء</option>
                </select>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">الفترة</span>
                <select className="form-input w-40">
                  <option>الشهر الحالي</option>
                  <option>الشهر الماضي</option>
                  <option>آخر 3 أشهر</option>
                </select>
              </div>
              <div className="flex gap-2">
                <button className="btn-primary flex-1">عرض التقرير</button>
                <button className="btn-secondary">تصدير Excel</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

const AccountantShipments = () => {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">الشحنات</h1>
        <p className="mt-1 text-sm text-gray-600">
          عرض الشحنات وحالة المدفوعات
        </p>
      </div>

      <div className="card">
        <div className="px-4 py-5 sm:p-6">
          <div className="text-center py-8 text-gray-500">
            لا توجد شحنات حالياً
          </div>
        </div>
      </div>
    </div>
  )
}

export default AccountantDashboard
