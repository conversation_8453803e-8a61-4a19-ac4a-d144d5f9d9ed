@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: 'Cairo', 'Tajawal', system-ui, sans-serif;
  line-height: 1.6;
  font-weight: 400;
  
  color-scheme: light;
  color: #1f2937;
  background-color: #ffffff;
  
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

html {
  direction: rtl;
  text-align: right;
}

body {
  margin: 0;
  min-height: 100vh;
  font-family: 'Cairo', 'Tajawal', system-ui, sans-serif;
  background-color: #f8fafc;
  color: #1f2937;
}

#root {
  min-height: 100vh;
}

/* Arabic text improvements */
.arabic-text {
  font-family: 'Cairo', 'Tajawal', system-ui, sans-serif;
  text-align: right;
  direction: rtl;
}

/* Custom scrollbar for RTL */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* RTL specific utilities */
.rtl {
  direction: rtl;
  text-align: right;
}

.ltr {
  direction: ltr;
  text-align: left;
}

/* Custom button styles */
.btn-primary {
  @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-secondary {
  @apply bg-secondary-100 hover:bg-secondary-200 text-secondary-700 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-danger {
  @apply bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

/* Form styles */
.form-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

/* Card styles */
.card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

/* Loading spinner */
.spinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600;
}
