@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');
@import "tailwindcss";

@theme {
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;

  --color-secondary-50: #f8fafc;
  --color-secondary-100: #f1f5f9;
  --color-secondary-200: #e2e8f0;
  --color-secondary-300: #cbd5e1;
  --color-secondary-400: #94a3b8;
  --color-secondary-500: #64748b;
  --color-secondary-600: #475569;
  --color-secondary-700: #334155;
  --color-secondary-800: #1e293b;
  --color-secondary-900: #0f172a;

  --font-family-arabic: 'Cairo', 'Tajawal', system-ui, sans-serif;
}

:root {
  font-family: 'Cairo', 'Tajawal', system-ui, sans-serif;
  line-height: 1.6;
  font-weight: 400;
  
  color-scheme: light;
  color: #1f2937;
  background-color: #ffffff;
  
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

html {
  direction: rtl;
  text-align: right;
}

body {
  margin: 0;
  min-height: 100vh;
  font-family: 'Cairo', 'Tajawal', system-ui, sans-serif;
  background-color: #f8fafc;
  color: #1f2937;
}

#root {
  min-height: 100vh;
}

/* Arabic text improvements */
.arabic-text {
  font-family: 'Cairo', 'Tajawal', system-ui, sans-serif;
  text-align: right;
  direction: rtl;
}

/* Custom scrollbar for RTL */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* RTL specific utilities */
.rtl {
  direction: rtl;
  text-align: right;
}

.ltr {
  direction: ltr;
  text-align: left;
}

/* Custom button styles */
.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-secondary {
  @apply bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-danger {
  @apply bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

/* Form styles */
.form-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

/* Card styles */
.card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

/* Loading spinner */
.spinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600;
}
