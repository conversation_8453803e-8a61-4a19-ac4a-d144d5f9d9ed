{"name": "backend", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "node prisma/seed.js", "test": "echo \"Error: no test specified\" && exit 1"}, "prisma": {"seed": "node prisma/seed.js"}, "keywords": ["shipping", "logistics", "china", "libya", "express", "api"], "author": "Shipping Management System", "license": "MIT", "description": "Backend API for China-Libya Shipping Management System", "dependencies": {"@prisma/client": "^6.12.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.1", "multer": "^2.0.2", "prisma": "^6.12.0"}, "devDependencies": {"nodemon": "^3.1.10"}}