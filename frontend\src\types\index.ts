// User Types
export type UserRole = 'CUSTOMER' | 'EMPLOYEE' | 'ACCOUNTANT' | 'ADMIN'

export interface User {
  id: string
  email: string
  role: UserRole
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface Customer {
  id: string
  userId: string
  fullName: string
  phone: string
  address?: string
  city?: string
  country: string
  companyName?: string
  taxNumber?: string
  notes?: string
  createdAt: string
  updatedAt: string
}

export interface Employee {
  id: string
  userId: string
  fullName: string
  phone: string
  position: string
  department?: string
  hireDate: string
  salary?: number
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface Accountant {
  id: string
  userId: string
  fullName: string
  phone: string
  createdAt: string
  updatedAt: string
}

export interface Admin {
  id: string
  userId: string
  fullName: string
  phone: string
  createdAt: string
  updatedAt: string
}

// Shipment Types
export type ShipmentStatus = 
  | 'PREPARING' 
  | 'SHIPPED' 
  | 'IN_TRANSIT' 
  | 'CUSTOMS' 
  | 'OUT_FOR_DELIVERY' 
  | 'DELIVERED' 
  | 'CANCELLED' 
  | 'RETURNED'

export interface Shipment {
  id: string
  trackingNumber: string
  customerId: string
  originCountry: string
  originCity?: string
  destinationCountry: string
  destinationCity?: string
  weight: number
  dimensions?: string
  description: string
  status: ShipmentStatus
  totalAmount: number
  paidAmount: number
  remainingAmount: number
  currency: string
  estimatedDelivery?: string
  actualDelivery?: string
  notes?: string
  createdById?: string
  updatedById?: string
  createdAt: string
  updatedAt: string
  customer?: Customer
  payments?: Payment[]
  statusHistory?: ShipmentStatusHistory[]
  documents?: ShipmentDocument[]
}

export interface ShipmentStatusHistory {
  id: string
  shipmentId: string
  status: ShipmentStatus
  notes?: string
  createdAt: string
}

export interface ShipmentDocument {
  id: string
  shipmentId: string
  fileName: string
  filePath: string
  fileSize: number
  fileType: string
  uploadedAt: string
}

// Payment Types
export type PaymentMethod = 
  | 'CASH' 
  | 'BANK_TRANSFER' 
  | 'CREDIT_CARD' 
  | 'CHECK' 
  | 'ONLINE' 
  | 'OTHER'

export interface Payment {
  id: string
  shipmentId: string
  amount: number
  currency: string
  paymentMethod: PaymentMethod
  paymentDate: string
  receiptNumber?: string
  receiptFile?: string
  notes?: string
  processedById?: string
  createdAt: string
  updatedAt: string
  shipment?: Shipment
}

// Notification Types
export type NotificationType = 'INFO' | 'SUCCESS' | 'WARNING' | 'ERROR' | 'SHIPMENT' | 'PAYMENT'

export interface Notification {
  id: string
  userId: string
  title: string
  message: string
  type: NotificationType
  isRead: boolean
  createdAt: string
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

// Form Types
export interface LoginForm {
  email: string
  password: string
}

export interface CreateShipmentForm {
  customerId: string
  originCity?: string
  destinationCity?: string
  weight: number
  dimensions?: string
  description: string
  totalAmount: number
  estimatedDelivery?: string
  notes?: string
}

export interface CreatePaymentForm {
  shipmentId: string
  amount: number
  paymentMethod: PaymentMethod
  receiptNumber?: string
  receiptFile?: File
  notes?: string
}

// Dashboard Stats
export interface DashboardStats {
  totalShipments: number
  activeShipments: number
  deliveredShipments: number
  totalRevenue: number
  pendingPayments: number
  recentShipments: Shipment[]
  recentPayments: Payment[]
}
