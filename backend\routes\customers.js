import express from 'express';
import { body, validationResult } from 'express-validator';
import prisma from '../config/database.js';
import { authenticateToken, requireRole, requireOwnershipOrRole } from '../middleware/auth.js';

const router = express.Router();

// Get all customers (Admin/Employee only)
router.get('/', [
  authenticateToken,
  requireRole(['ADMIN', 'EMPLOYEE', 'ACCOUNTANT'])
], async (req, res) => {
  try {
    const { page = 1, limit = 10, search = '' } = req.query;
    const skip = (page - 1) * limit;

    const where = search ? {
      OR: [
        { fullName: { contains: search, mode: 'insensitive' } },
        { phone: { contains: search } },
        { companyName: { contains: search, mode: 'insensitive' } },
        { user: { email: { contains: search, mode: 'insensitive' } } }
      ]
    } : {};

    const [customers, total] = await Promise.all([
      prisma.customer.findMany({
        where,
        include: {
          user: {
            select: { id: true, email: true, isActive: true, createdAt: true }
          },
          _count: {
            select: { shipments: true }
          }
        },
        skip: parseInt(skip),
        take: parseInt(limit),
        orderBy: { createdAt: 'desc' }
      }),
      prisma.customer.count({ where })
    ]);

    res.json({
      success: true,
      data: {
        customers,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          totalPages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('Get customers error:', error);
    res.status(500).json({
      success: false,
      error: 'خطأ في الخادم',
      message: 'Internal server error'
    });
  }
});

// Get customer by ID
router.get('/:id', [
  authenticateToken,
  requireOwnershipOrRole(['ADMIN', 'EMPLOYEE', 'ACCOUNTANT'])
], async (req, res) => {
  try {
    const { id } = req.params;

    const customer = await prisma.customer.findUnique({
      where: { id },
      include: {
        user: {
          select: { id: true, email: true, isActive: true, createdAt: true }
        },
        shipments: {
          select: {
            id: true,
            trackingNumber: true,
            status: true,
            totalAmount: true,
            paidAmount: true,
            remainingAmount: true,
            createdAt: true
          },
          orderBy: { createdAt: 'desc' },
          take: 10
        },
        _count: {
          select: { shipments: true }
        }
      }
    });

    if (!customer) {
      return res.status(404).json({
        success: false,
        error: 'العميل غير موجود',
        message: 'Customer not found'
      });
    }

    // Check if user is accessing their own data
    if (req.user.role === 'CUSTOMER' && customer.userId !== req.user.id) {
      return res.status(403).json({
        success: false,
        error: 'ليس لديك صلاحية للوصول إلى هذا المورد',
        message: 'Access denied'
      });
    }

    res.json({
      success: true,
      data: customer
    });

  } catch (error) {
    console.error('Get customer error:', error);
    res.status(500).json({
      success: false,
      error: 'خطأ في الخادم',
      message: 'Internal server error'
    });
  }
});

// Create new customer (Admin/Employee only)
router.post('/', [
  authenticateToken,
  requireRole(['ADMIN', 'EMPLOYEE']),
  body('email').isEmail().normalizeEmail().withMessage('البريد الإلكتروني غير صحيح'),
  body('password').isLength({ min: 6 }).withMessage('كلمة المرور يجب أن تكون 6 أحرف على الأقل'),
  body('fullName').notEmpty().withMessage('الاسم الكامل مطلوب'),
  body('phone').notEmpty().withMessage('رقم الهاتف مطلوب')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'بيانات غير صحيحة',
        message: 'Validation failed',
        details: errors.array()
      });
    }

    const { email, password, fullName, phone, address, city, country = 'Libya', companyName, taxNumber, notes } = req.body;

    // Check if email already exists
    const existingUser = await prisma.user.findUnique({
      where: { email }
    });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        error: 'البريد الإلكتروني مستخدم بالفعل',
        message: 'Email already exists'
      });
    }

    // Hash password
    const { hashPassword } = await import('../utils/auth.js');
    const hashedPassword = await hashPassword(password);

    // Create user and customer
    const result = await prisma.user.create({
      data: {
        email,
        password: hashedPassword,
        role: 'CUSTOMER',
        isActive: true,
        customer: {
          create: {
            fullName,
            phone,
            address,
            city,
            country,
            companyName,
            taxNumber,
            notes
          }
        }
      },
      include: {
        customer: true
      }
    });

    // Remove password from response
    const { password: _, ...userWithoutPassword } = result;

    res.status(201).json({
      success: true,
      message: 'تم إنشاء العميل بنجاح',
      data: userWithoutPassword
    });

  } catch (error) {
    console.error('Create customer error:', error);
    res.status(500).json({
      success: false,
      error: 'خطأ في الخادم',
      message: 'Internal server error'
    });
  }
});

// Update customer
router.put('/:id', [
  authenticateToken,
  requireOwnershipOrRole(['ADMIN', 'EMPLOYEE']),
  body('fullName').optional().notEmpty().withMessage('الاسم الكامل لا يمكن أن يكون فارغاً'),
  body('phone').optional().notEmpty().withMessage('رقم الهاتف لا يمكن أن يكون فارغاً')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'بيانات غير صحيحة',
        message: 'Validation failed',
        details: errors.array()
      });
    }

    const { id } = req.params;
    const { fullName, phone, address, city, country, companyName, taxNumber, notes } = req.body;

    const customer = await prisma.customer.findUnique({
      where: { id }
    });

    if (!customer) {
      return res.status(404).json({
        success: false,
        error: 'العميل غير موجود',
        message: 'Customer not found'
      });
    }

    // Check if user is updating their own data
    if (req.user.role === 'CUSTOMER' && customer.userId !== req.user.id) {
      return res.status(403).json({
        success: false,
        error: 'ليس لديك صلاحية لتعديل هذا المورد',
        message: 'Access denied'
      });
    }

    const updatedCustomer = await prisma.customer.update({
      where: { id },
      data: {
        fullName,
        phone,
        address,
        city,
        country,
        companyName,
        taxNumber,
        notes
      },
      include: {
        user: {
          select: { id: true, email: true, isActive: true }
        }
      }
    });

    res.json({
      success: true,
      message: 'تم تحديث بيانات العميل بنجاح',
      data: updatedCustomer
    });

  } catch (error) {
    console.error('Update customer error:', error);
    res.status(500).json({
      success: false,
      error: 'خطأ في الخادم',
      message: 'Internal server error'
    });
  }
});

// Get customer shipments
router.get('/:id/shipments', [
  authenticateToken,
  requireOwnershipOrRole(['ADMIN', 'EMPLOYEE', 'ACCOUNTANT'])
], async (req, res) => {
  try {
    const { id } = req.params;
    const { page = 1, limit = 10, status } = req.query;
    const skip = (page - 1) * limit;

    const customer = await prisma.customer.findUnique({
      where: { id }
    });

    if (!customer) {
      return res.status(404).json({
        success: false,
        error: 'العميل غير موجود',
        message: 'Customer not found'
      });
    }

    // Check if user is accessing their own data
    if (req.user.role === 'CUSTOMER' && customer.userId !== req.user.id) {
      return res.status(403).json({
        success: false,
        error: 'ليس لديك صلاحية للوصول إلى هذا المورد',
        message: 'Access denied'
      });
    }

    const where = {
      customerId: id,
      ...(status && { status })
    };

    const [shipments, total] = await Promise.all([
      prisma.shipment.findMany({
        where,
        include: {
          payments: {
            select: {
              id: true,
              amount: true,
              paymentMethod: true,
              paymentDate: true
            }
          },
          _count: {
            select: { payments: true }
          }
        },
        skip: parseInt(skip),
        take: parseInt(limit),
        orderBy: { createdAt: 'desc' }
      }),
      prisma.shipment.count({ where })
    ]);

    res.json({
      success: true,
      data: {
        shipments,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          totalPages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('Get customer shipments error:', error);
    res.status(500).json({
      success: false,
      error: 'خطأ في الخادم',
      message: 'Internal server error'
    });
  }
});

export default router;
