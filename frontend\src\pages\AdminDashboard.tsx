import { Routes, Route } from 'react-router-dom'
import { UserGroupIcon, CogIcon, ServerIcon, ChartBarIcon } from '@heroicons/react/24/outline'

const AdminDashboard = () => {
  return (
    <Routes>
      <Route path="/" element={<AdminHome />} />
      <Route path="/users" element={<AdminUsers />} />
      <Route path="/shipments" element={<AdminShipments />} />
      <Route path="/payments" element={<AdminPayments />} />
      <Route path="/settings" element={<AdminSettings />} />
    </Routes>
  )
}

const AdminHome = () => {
  const stats = [
    { name: 'إجمالي المستخدمين', value: '127', icon: UserGroupIcon, color: 'bg-blue-500' },
    { name: 'إجمالي الشحنات', value: '1,234', icon: ChartBarIcon, color: 'bg-green-500' },
    { name: 'الإيرادات الشهرية', value: '$12,345', icon: ChartBarIcon, color: 'bg-purple-500' },
    { name: 'حالة النظام', value: 'نشط', icon: ServerIcon, color: 'bg-green-500' },
  ]

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">لوحة المدير</h1>
        <p className="mt-1 text-sm text-gray-600">
          إدارة النظام والمستخدمين
        </p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <div key={stat.name} className="card">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className={`${stat.color} rounded-md p-3`}>
                  <stat.icon className="h-6 w-6 text-white" />
                </div>
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    {stat.name}
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {stat.value}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* System Overview */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <div className="card">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              نشاط النظام الأخير
            </h3>
            <div className="text-center py-8 text-gray-500">
              لا توجد أنشطة حديثة
            </div>
          </div>
        </div>

        <div className="card">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              المستخدمين الجدد
            </h3>
            <div className="text-center py-8 text-gray-500">
              لا يوجد مستخدمين جدد
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

const AdminUsers = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">إدارة المستخدمين</h1>
          <p className="mt-1 text-sm text-gray-600">
            إدارة جميع مستخدمي النظام
          </p>
        </div>
        <button className="btn-primary">
          <UserGroupIcon className="h-5 w-5 ml-2" />
          إضافة مستخدم جديد
        </button>
      </div>

      <div className="card">
        <div className="px-4 py-5 sm:p-6">
          <div className="text-center py-8 text-gray-500">
            لا يوجد مستخدمين حالياً
          </div>
        </div>
      </div>
    </div>
  )
}

const AdminShipments = () => {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">جميع الشحنات</h1>
        <p className="mt-1 text-sm text-gray-600">
          عرض ومراقبة جميع الشحنات في النظام
        </p>
      </div>

      <div className="card">
        <div className="px-4 py-5 sm:p-6">
          <div className="text-center py-8 text-gray-500">
            لا توجد شحنات حالياً
          </div>
        </div>
      </div>
    </div>
  )
}

const AdminPayments = () => {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">جميع المدفوعات</h1>
        <p className="mt-1 text-sm text-gray-600">
          مراقبة جميع المدفوعات في النظام
        </p>
      </div>

      <div className="card">
        <div className="px-4 py-5 sm:p-6">
          <div className="text-center py-8 text-gray-500">
            لا توجد مدفوعات حالياً
          </div>
        </div>
      </div>
    </div>
  )
}

const AdminSettings = () => {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">إعدادات النظام</h1>
        <p className="mt-1 text-sm text-gray-600">
          إدارة إعدادات النظام والشركة
        </p>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <div className="card">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              معلومات الشركة
            </h3>
            <form className="space-y-4">
              <div>
                <label className="form-label">اسم الشركة</label>
                <input type="text" className="form-input" placeholder="نظام إدارة الشحن" />
              </div>
              <div>
                <label className="form-label">البريد الإلكتروني</label>
                <input type="email" className="form-input" placeholder="<EMAIL>" />
              </div>
              <div>
                <label className="form-label">رقم الهاتف</label>
                <input type="tel" className="form-input" placeholder="+218-XX-XXXXXXX" />
              </div>
              <button type="submit" className="btn-primary w-full">
                حفظ التغييرات
              </button>
            </form>
          </div>
        </div>

        <div className="card">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              إعدادات النظام
            </h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">الإشعارات</span>
                <input type="checkbox" className="rounded" defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">النسخ الاحتياطي التلقائي</span>
                <input type="checkbox" className="rounded" defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">تسجيل الأنشطة</span>
                <input type="checkbox" className="rounded" defaultChecked />
              </div>
              <button className="btn-primary w-full">
                حفظ الإعدادات
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AdminDashboard
