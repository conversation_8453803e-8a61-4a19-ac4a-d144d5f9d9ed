import { ReactNode, useState } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { useAuthStore } from '../stores/authStore'
import { 
  HomeIcon, 
  TruckIcon, 
  CreditCardIcon, 
  UserGroupIcon,
  CogIcon,
  BellIcon,
  Bars3Icon,
  XMarkIcon
} from '@heroicons/react/24/outline'

interface LayoutProps {
  children: ReactNode
}

const Layout = ({ children }: LayoutProps) => {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const { user, profile, logout } = useAuthStore()
  const location = useLocation()

  const getNavigationItems = () => {
    const baseItems = [
      { name: 'الرئيسية', href: '', icon: HomeIcon },
    ]

    switch (user?.role) {
      case 'CUSTOMER':
        return [
          ...baseItems,
          { name: 'شحناتي', href: '/shipments', icon: TruckIcon },
          { name: 'المدفوعات', href: '/payments', icon: CreditCardIcon },
          { name: 'تتبع الشحنة', href: '/tracking', icon: TruckIcon },
        ]
      case 'EMPLOYEE':
        return [
          ...baseItems,
          { name: 'العملاء', href: '/customers', icon: UserGroupIcon },
          { name: 'الشحنات', href: '/shipments', icon: TruckIcon },
          { name: 'إنشاء شحنة', href: '/shipments/create', icon: TruckIcon },
        ]
      case 'ACCOUNTANT':
        return [
          ...baseItems,
          { name: 'المدفوعات', href: '/payments', icon: CreditCardIcon },
          { name: 'التقارير المالية', href: '/reports', icon: CreditCardIcon },
          { name: 'الشحنات', href: '/shipments', icon: TruckIcon },
        ]
      case 'ADMIN':
        return [
          ...baseItems,
          { name: 'المستخدمين', href: '/users', icon: UserGroupIcon },
          { name: 'الشحنات', href: '/shipments', icon: TruckIcon },
          { name: 'المدفوعات', href: '/payments', icon: CreditCardIcon },
          { name: 'الإعدادات', href: '/settings', icon: CogIcon },
        ]
      default:
        return baseItems
    }
  }

  const navigationItems = getNavigationItems()
  const basePath = `/${user?.role?.toLowerCase()}`

  const isActive = (href: string) => {
    const fullPath = basePath + href
    return location.pathname === fullPath || 
           (href === '' && location.pathname === basePath)
  }

  return (
    <div className="min-h-screen bg-gray-50 rtl">
      {/* Mobile sidebar */}
      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)} />
        <div className="fixed inset-y-0 right-0 flex w-full max-w-xs flex-col bg-white shadow-xl">
          <div className="flex h-16 items-center justify-between px-4">
            <h2 className="text-lg font-semibold text-gray-900">نظام إدارة الشحن</h2>
            <button
              onClick={() => setSidebarOpen(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>
          <nav className="flex-1 space-y-1 px-4 py-4">
            {navigationItems.map((item) => (
              <Link
                key={item.name}
                to={basePath + item.href}
                className={`group flex items-center rounded-md px-2 py-2 text-sm font-medium ${
                  isActive(item.href)
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
                onClick={() => setSidebarOpen(false)}
              >
                <item.icon className="ml-3 h-5 w-5 flex-shrink-0" />
                {item.name}
              </Link>
            ))}
          </nav>
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:right-0 lg:z-50 lg:flex lg:w-72 lg:flex-col">
        <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-white px-6 shadow-lg">
          <div className="flex h-16 shrink-0 items-center">
            <h1 className="text-xl font-bold text-gray-900">نظام إدارة الشحن</h1>
          </div>
          <nav className="flex flex-1 flex-col">
            <ul className="flex flex-1 flex-col gap-y-7">
              <li>
                <ul className="-mx-2 space-y-1">
                  {navigationItems.map((item) => (
                    <li key={item.name}>
                      <Link
                        to={basePath + item.href}
                        className={`group flex gap-x-3 rounded-md p-2 text-sm font-semibold leading-6 ${
                          isActive(item.href)
                            ? 'bg-blue-50 text-blue-700'
                            : 'text-gray-700 hover:bg-gray-50 hover:text-blue-700'
                        }`}
                      >
                        <item.icon className="h-6 w-6 shrink-0" />
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </li>
            </ul>
          </nav>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pr-72">
        {/* Top bar */}
        <div className="sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8">
          <button
            type="button"
            className="-m-2.5 p-2.5 text-gray-700 lg:hidden"
            onClick={() => setSidebarOpen(true)}
          >
            <Bars3Icon className="h-6 w-6" />
          </button>

          <div className="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
            <div className="flex flex-1" />
            <div className="flex items-center gap-x-4 lg:gap-x-6">
              {/* Notifications */}
              <button className="relative -m-2.5 p-2.5 text-gray-400 hover:text-gray-500">
                <BellIcon className="h-6 w-6" />
                <span className="absolute -top-1 -right-1 h-4 w-4 rounded-full bg-red-500 text-xs text-white flex items-center justify-center">
                  3
                </span>
              </button>

              {/* Profile dropdown */}
              <div className="relative">
                <div className="flex items-center gap-x-3">
                  <div className="text-right">
                    <p className="text-sm font-semibold text-gray-900">
                      {profile?.fullName || user?.email}
                    </p>
                    <p className="text-xs text-gray-500">
                      {user?.role === 'CUSTOMER' && 'عميل'}
                      {user?.role === 'EMPLOYEE' && 'موظف'}
                      {user?.role === 'ACCOUNTANT' && 'محاسب'}
                      {user?.role === 'ADMIN' && 'مدير النظام'}
                    </p>
                  </div>
                  <button
                    onClick={logout}
                    className="rounded-md bg-red-600 px-3 py-1.5 text-sm font-semibold text-white shadow-sm hover:bg-red-500"
                  >
                    تسجيل الخروج
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="py-6">
          <div className="px-4 sm:px-6 lg:px-8">
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}

export default Layout
