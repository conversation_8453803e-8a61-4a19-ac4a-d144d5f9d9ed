import axios from 'axios'
import { ApiResponse } from '../types'

// Create axios instance
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3001',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('token')
      window.location.href = '/login'
    }
    
    return Promise.reject(error)
  }
)

// Auth API
export const authAPI = {
  login: (email: string, password: string) =>
    api.post<ApiResponse>('/api/auth/login', { email, password }),
  
  register: (userData: any) =>
    api.post<ApiResponse>('/api/auth/register', userData),
  
  logout: () =>
    api.post<ApiResponse>('/api/auth/logout'),
  
  me: () =>
    api.get<ApiResponse>('/api/auth/me'),
  
  refreshToken: () =>
    api.post<ApiResponse>('/api/auth/refresh'),
}

// Users API
export const usersAPI = {
  getAll: (params?: any) =>
    api.get<ApiResponse>('/api/users', { params }),
  
  getById: (id: string) =>
    api.get<ApiResponse>(`/api/users/${id}`),
  
  create: (userData: any) =>
    api.post<ApiResponse>('/api/users', userData),
  
  update: (id: string, userData: any) =>
    api.put<ApiResponse>(`/api/users/${id}`, userData),
  
  delete: (id: string) =>
    api.delete<ApiResponse>(`/api/users/${id}`),
  
  activate: (id: string) =>
    api.patch<ApiResponse>(`/api/users/${id}/activate`),
  
  deactivate: (id: string) =>
    api.patch<ApiResponse>(`/api/users/${id}/deactivate`),
}

// Customers API
export const customersAPI = {
  getAll: (params?: any) =>
    api.get<ApiResponse>('/api/customers', { params }),
  
  getById: (id: string) =>
    api.get<ApiResponse>(`/api/customers/${id}`),
  
  create: (customerData: any) =>
    api.post<ApiResponse>('/api/customers', customerData),
  
  update: (id: string, customerData: any) =>
    api.put<ApiResponse>(`/api/customers/${id}`, customerData),
  
  delete: (id: string) =>
    api.delete<ApiResponse>(`/api/customers/${id}`),
  
  getShipments: (id: string, params?: any) =>
    api.get<ApiResponse>(`/api/customers/${id}/shipments`, { params }),
}

// Shipments API
export const shipmentsAPI = {
  getAll: (params?: any) =>
    api.get<ApiResponse>('/api/shipments', { params }),
  
  getById: (id: string) =>
    api.get<ApiResponse>(`/api/shipments/${id}`),
  
  create: (shipmentData: any) =>
    api.post<ApiResponse>('/api/shipments', shipmentData),
  
  update: (id: string, shipmentData: any) =>
    api.put<ApiResponse>(`/api/shipments/${id}`, shipmentData),
  
  delete: (id: string) =>
    api.delete<ApiResponse>(`/api/shipments/${id}`),
  
  updateStatus: (id: string, status: string, notes?: string) =>
    api.patch<ApiResponse>(`/api/shipments/${id}/status`, { status, notes }),
  
  track: (trackingNumber: string) =>
    api.get<ApiResponse>(`/api/shipments/track/${trackingNumber}`),
  
  getStatusHistory: (id: string) =>
    api.get<ApiResponse>(`/api/shipments/${id}/status-history`),
  
  uploadDocument: (id: string, file: File) => {
    const formData = new FormData()
    formData.append('document', file)
    return api.post<ApiResponse>(`/api/shipments/${id}/documents`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },
  
  getDocuments: (id: string) =>
    api.get<ApiResponse>(`/api/shipments/${id}/documents`),
}

// Payments API
export const paymentsAPI = {
  getAll: (params?: any) =>
    api.get<ApiResponse>('/api/payments', { params }),
  
  getById: (id: string) =>
    api.get<ApiResponse>(`/api/payments/${id}`),
  
  create: (paymentData: any) =>
    api.post<ApiResponse>('/api/payments', paymentData),
  
  update: (id: string, paymentData: any) =>
    api.put<ApiResponse>(`/api/payments/${id}`, paymentData),
  
  delete: (id: string) =>
    api.delete<ApiResponse>(`/api/payments/${id}`),
  
  uploadReceipt: (id: string, file: File) => {
    const formData = new FormData()
    formData.append('receipt', file)
    return api.post<ApiResponse>(`/api/payments/${id}/receipt`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },
  
  getByShipment: (shipmentId: string) =>
    api.get<ApiResponse>(`/api/payments/shipment/${shipmentId}`),
}

// Dashboard API
export const dashboardAPI = {
  getStats: () =>
    api.get<ApiResponse>('/api/dashboard/stats'),
}

// Reports API
export const reportsAPI = {
  getDashboardStats: () =>
    api.get<ApiResponse>('/api/reports/dashboard'),
  
  getFinancialReport: (params: any) =>
    api.get<ApiResponse>('/api/reports/financial', { params }),
  
  getShipmentReport: (params: any) =>
    api.get<ApiResponse>('/api/reports/shipments', { params }),
  
  exportFinancial: (params: any) =>
    api.get('/api/reports/financial/export', { 
      params, 
      responseType: 'blob' 
    }),
  
  exportShipments: (params: any) =>
    api.get('/api/reports/shipments/export', { 
      params, 
      responseType: 'blob' 
    }),
}

// Settings API
export const settingsAPI = {
  getAll: () =>
    api.get<ApiResponse>('/api/settings'),
  
  update: (settings: any) =>
    api.put<ApiResponse>('/api/settings', settings),
  
  getCompanyInfo: () =>
    api.get<ApiResponse>('/api/settings/company'),
  
  updateCompanyInfo: (companyData: any) =>
    api.put<ApiResponse>('/api/settings/company', companyData),
}

// Notifications API
export const notificationsAPI = {
  getAll: (params?: any) =>
    api.get<ApiResponse>('/api/notifications', { params }),
  
  markAsRead: (id: string) =>
    api.patch<ApiResponse>(`/api/notifications/${id}/read`),
  
  markAllAsRead: () =>
    api.patch<ApiResponse>('/api/notifications/read-all'),
  
  delete: (id: string) =>
    api.delete<ApiResponse>(`/api/notifications/${id}`),
}

export default api
