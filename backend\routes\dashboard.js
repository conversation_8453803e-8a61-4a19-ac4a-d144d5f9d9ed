import express from 'express';
import prisma from '../config/database.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// Get dashboard statistics
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    let stats = {};

    switch (req.user.role) {
      case 'CUSTOMER':
        stats = await getCustomerStats(req.user.id);
        break;
      case 'EMPLOYEE':
        stats = await getEmployeeStats();
        break;
      case 'ACCOUNTANT':
        stats = await getAccountantStats();
        break;
      case 'ADMIN':
        stats = await getAdminStats();
        break;
      default:
        return res.status(403).json({
          success: false,
          error: 'ليس لديك صلاحية للوصول إلى هذا المورد',
          message: 'Access denied'
        });
    }

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('Get dashboard stats error:', error);
    res.status(500).json({
      success: false,
      error: 'خطأ في الخادم',
      message: 'Internal server error'
    });
  }
});

// Customer dashboard stats
async function getCustomerStats(userId) {
  const customer = await prisma.customer.findUnique({
    where: { userId }
  });

  if (!customer) {
    throw new Error('Customer profile not found');
  }

  const [
    totalShipments,
    activeShipments,
    deliveredShipments,
    pendingPayments,
    recentShipments,
    recentPayments
  ] = await Promise.all([
    // Total shipments
    prisma.shipment.count({
      where: { customerId: customer.id }
    }),
    
    // Active shipments (not delivered, cancelled, or returned)
    prisma.shipment.count({
      where: {
        customerId: customer.id,
        status: {
          notIn: ['DELIVERED', 'CANCELLED', 'RETURNED']
        }
      }
    }),
    
    // Delivered shipments
    prisma.shipment.count({
      where: {
        customerId: customer.id,
        status: 'DELIVERED'
      }
    }),
    
    // Pending payments (shipments with remaining amount > 0)
    prisma.shipment.count({
      where: {
        customerId: customer.id,
        remainingAmount: { gt: 0 }
      }
    }),
    
    // Recent shipments (last 5)
    prisma.shipment.findMany({
      where: { customerId: customer.id },
      select: {
        id: true,
        trackingNumber: true,
        status: true,
        totalAmount: true,
        paidAmount: true,
        remainingAmount: true,
        createdAt: true,
        description: true
      },
      orderBy: { createdAt: 'desc' },
      take: 5
    }),
    
    // Recent payments (last 5)
    prisma.payment.findMany({
      where: {
        shipment: { customerId: customer.id }
      },
      select: {
        id: true,
        amount: true,
        currency: true,
        paymentMethod: true,
        paymentDate: true,
        shipment: {
          select: {
            trackingNumber: true
          }
        }
      },
      orderBy: { paymentDate: 'desc' },
      take: 5
    })
  ]);

  return {
    totalShipments,
    activeShipments,
    deliveredShipments,
    pendingPayments,
    recentShipments,
    recentPayments
  };
}

// Employee dashboard stats
async function getEmployeeStats() {
  const [
    totalCustomers,
    shipmentsToday,
    activeShipments,
    totalShipments
  ] = await Promise.all([
    prisma.customer.count(),
    
    prisma.shipment.count({
      where: {
        createdAt: {
          gte: new Date(new Date().setHours(0, 0, 0, 0))
        }
      }
    }),
    
    prisma.shipment.count({
      where: {
        status: {
          notIn: ['DELIVERED', 'CANCELLED', 'RETURNED']
        }
      }
    }),
    
    prisma.shipment.count()
  ]);

  return {
    totalCustomers,
    shipmentsToday,
    activeShipments,
    totalShipments
  };
}

// Accountant dashboard stats
async function getAccountantStats() {
  const today = new Date();
  const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

  const [
    totalRevenue,
    paymentsToday,
    pendingPayments,
    monthlyReports,
    recentPayments
  ] = await Promise.all([
    // Total revenue
    prisma.payment.aggregate({
      _sum: { amount: true }
    }).then(result => result._sum.amount || 0),
    
    // Payments today
    prisma.payment.count({
      where: {
        paymentDate: {
          gte: new Date(new Date().setHours(0, 0, 0, 0))
        }
      }
    }),
    
    // Pending payments
    prisma.shipment.count({
      where: {
        remainingAmount: { gt: 0 }
      }
    }),
    
    // Monthly reports (placeholder)
    3,
    
    // Recent payments
    prisma.payment.findMany({
      include: {
        shipment: {
          select: {
            trackingNumber: true,
            customer: {
              select: {
                fullName: true
              }
            }
          }
        }
      },
      orderBy: { paymentDate: 'desc' },
      take: 10
    })
  ]);

  return {
    totalRevenue,
    paymentsToday,
    pendingPayments,
    monthlyReports,
    recentPayments
  };
}

// Admin dashboard stats
async function getAdminStats() {
  const [
    totalUsers,
    totalShipments,
    monthlyRevenue,
    systemStatus,
    recentActivity
  ] = await Promise.all([
    prisma.user.count(),
    
    prisma.shipment.count(),
    
    // Monthly revenue
    prisma.payment.aggregate({
      where: {
        paymentDate: {
          gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
        }
      },
      _sum: { amount: true }
    }).then(result => result._sum.amount || 0),
    
    // System status (placeholder)
    'نشط',
    
    // Recent activity (placeholder)
    []
  ]);

  return {
    totalUsers,
    totalShipments,
    monthlyRevenue,
    systemStatus,
    recentActivity
  };
}

export default router;
