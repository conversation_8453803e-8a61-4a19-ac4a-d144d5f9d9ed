import express from 'express';
import { body, validationResult } from 'express-validator';
import prisma from '../config/database.js';
import { authenticateToken, requireRole, requireOwnershipOrRole } from '../middleware/auth.js';
import { generateTrackingNumber } from '../utils/auth.js';

const router = express.Router();

// Get all shipments
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 10, status, search } = req.query;
    const skip = (page - 1) * limit;

    let where = {};

    // Filter by customer for CUSTOMER role
    if (req.user.role === 'CUSTOMER') {
      const customer = await prisma.customer.findUnique({
        where: { userId: req.user.id }
      });
      if (!customer) {
        return res.status(404).json({
          success: false,
          error: 'بيانات العميل غير موجودة',
          message: 'Customer profile not found'
        });
      }
      where.customerId = customer.id;
    }

    // Add status filter
    if (status) {
      where.status = status;
    }

    // Add search filter
    if (search) {
      where.OR = [
        { trackingNumber: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { customer: { fullName: { contains: search, mode: 'insensitive' } } }
      ];
    }

    const [shipments, total] = await Promise.all([
      prisma.shipment.findMany({
        where,
        include: {
          customer: {
            select: {
              id: true,
              fullName: true,
              phone: true,
              companyName: true
            }
          },
          payments: {
            select: {
              id: true,
              amount: true,
              paymentMethod: true,
              paymentDate: true
            }
          },
          createdBy: {
            select: {
              id: true,
              fullName: true
            }
          },
          _count: {
            select: { payments: true, documents: true }
          }
        },
        skip: parseInt(skip),
        take: parseInt(limit),
        orderBy: { createdAt: 'desc' }
      }),
      prisma.shipment.count({ where })
    ]);

    res.json({
      success: true,
      data: {
        shipments,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          totalPages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('Get shipments error:', error);
    res.status(500).json({
      success: false,
      error: 'خطأ في الخادم',
      message: 'Internal server error'
    });
  }
});

// Get shipment by ID
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const shipment = await prisma.shipment.findUnique({
      where: { id },
      include: {
        customer: {
          select: {
            id: true,
            fullName: true,
            phone: true,
            address: true,
            city: true,
            country: true,
            companyName: true
          }
        },
        payments: {
          include: {
            processedBy: {
              select: {
                id: true,
                fullName: true
              }
            }
          },
          orderBy: { paymentDate: 'desc' }
        },
        statusHistory: {
          orderBy: { createdAt: 'desc' }
        },
        documents: true,
        createdBy: {
          select: {
            id: true,
            fullName: true
          }
        },
        updatedBy: {
          select: {
            id: true,
            fullName: true
          }
        }
      }
    });

    if (!shipment) {
      return res.status(404).json({
        success: false,
        error: 'الشحنة غير موجودة',
        message: 'Shipment not found'
      });
    }

    // Check if customer is accessing their own shipment
    if (req.user.role === 'CUSTOMER') {
      const customer = await prisma.customer.findUnique({
        where: { userId: req.user.id }
      });
      if (!customer || shipment.customerId !== customer.id) {
        return res.status(403).json({
          success: false,
          error: 'ليس لديك صلاحية للوصول إلى هذا المورد',
          message: 'Access denied'
        });
      }
    }

    res.json({
      success: true,
      data: shipment
    });

  } catch (error) {
    console.error('Get shipment error:', error);
    res.status(500).json({
      success: false,
      error: 'خطأ في الخادم',
      message: 'Internal server error'
    });
  }
});

// Create new shipment (Employee/Admin only)
router.post('/', [
  authenticateToken,
  requireRole(['ADMIN', 'EMPLOYEE']),
  body('customerId').notEmpty().withMessage('معرف العميل مطلوب'),
  body('weight').isFloat({ min: 0.1 }).withMessage('الوزن يجب أن يكون أكبر من 0'),
  body('description').notEmpty().withMessage('وصف الشحنة مطلوب'),
  body('totalAmount').isFloat({ min: 0 }).withMessage('المبلغ الإجمالي يجب أن يكون أكبر من أو يساوي 0')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'بيانات غير صحيحة',
        message: 'Validation failed',
        details: errors.array()
      });
    }

    const {
      customerId,
      originCity,
      destinationCity,
      weight,
      dimensions,
      description,
      totalAmount,
      currency = 'USD',
      estimatedDelivery,
      notes
    } = req.body;

    // Verify customer exists
    const customer = await prisma.customer.findUnique({
      where: { id: customerId }
    });

    if (!customer) {
      return res.status(404).json({
        success: false,
        error: 'العميل غير موجود',
        message: 'Customer not found'
      });
    }

    // Get employee profile
    const employee = await prisma.employee.findUnique({
      where: { userId: req.user.id }
    });

    // Generate tracking number
    const trackingNumber = generateTrackingNumber();

    // Create shipment
    const shipment = await prisma.shipment.create({
      data: {
        trackingNumber,
        customerId,
        originCity,
        destinationCity,
        weight: parseFloat(weight),
        dimensions,
        description,
        totalAmount: parseFloat(totalAmount),
        remainingAmount: parseFloat(totalAmount),
        currency,
        estimatedDelivery: estimatedDelivery ? new Date(estimatedDelivery) : null,
        notes,
        createdById: employee?.id,
        updatedById: employee?.id
      },
      include: {
        customer: {
          select: {
            id: true,
            fullName: true,
            phone: true,
            companyName: true
          }
        },
        createdBy: {
          select: {
            id: true,
            fullName: true
          }
        }
      }
    });

    // Create initial status history
    await prisma.shipmentStatusHistory.create({
      data: {
        shipmentId: shipment.id,
        status: 'PREPARING',
        notes: 'تم إنشاء الشحنة'
      }
    });

    res.status(201).json({
      success: true,
      message: 'تم إنشاء الشحنة بنجاح',
      data: shipment
    });

  } catch (error) {
    console.error('Create shipment error:', error);
    res.status(500).json({
      success: false,
      error: 'خطأ في الخادم',
      message: 'Internal server error'
    });
  }
});

// Update shipment status
router.patch('/:id/status', [
  authenticateToken,
  requireRole(['ADMIN', 'EMPLOYEE']),
  body('status').isIn(['PREPARING', 'SHIPPED', 'IN_TRANSIT', 'CUSTOMS', 'OUT_FOR_DELIVERY', 'DELIVERED', 'CANCELLED', 'RETURNED'])
    .withMessage('حالة الشحنة غير صحيحة')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'بيانات غير صحيحة',
        message: 'Validation failed',
        details: errors.array()
      });
    }

    const { id } = req.params;
    const { status, notes } = req.body;

    const shipment = await prisma.shipment.findUnique({
      where: { id }
    });

    if (!shipment) {
      return res.status(404).json({
        success: false,
        error: 'الشحنة غير موجودة',
        message: 'Shipment not found'
      });
    }

    // Get employee profile
    const employee = await prisma.employee.findUnique({
      where: { userId: req.user.id }
    });

    // Update shipment status
    const updatedShipment = await prisma.shipment.update({
      where: { id },
      data: {
        status,
        updatedById: employee?.id,
        ...(status === 'DELIVERED' && { actualDelivery: new Date() })
      },
      include: {
        customer: {
          select: {
            id: true,
            fullName: true,
            phone: true
          }
        }
      }
    });

    // Create status history entry
    await prisma.shipmentStatusHistory.create({
      data: {
        shipmentId: id,
        status,
        notes
      }
    });

    // Create notification for customer
    const customer = await prisma.customer.findUnique({
      where: { id: updatedShipment.customerId },
      include: { user: true }
    });

    if (customer?.user) {
      await prisma.notification.create({
        data: {
          userId: customer.user.id,
          title: 'تحديث حالة الشحنة',
          message: `تم تحديث حالة الشحنة ${updatedShipment.trackingNumber} إلى ${getStatusInArabic(status)}`,
          type: 'SHIPMENT'
        }
      });
    }

    res.json({
      success: true,
      message: 'تم تحديث حالة الشحنة بنجاح',
      data: updatedShipment
    });

  } catch (error) {
    console.error('Update shipment status error:', error);
    res.status(500).json({
      success: false,
      error: 'خطأ في الخادم',
      message: 'Internal server error'
    });
  }
});

// Track shipment by tracking number
router.get('/track/:trackingNumber', async (req, res) => {
  try {
    const { trackingNumber } = req.params;

    const shipment = await prisma.shipment.findUnique({
      where: { trackingNumber },
      include: {
        statusHistory: {
          orderBy: { createdAt: 'asc' }
        }
      }
    });

    if (!shipment) {
      return res.status(404).json({
        success: false,
        error: 'رقم التتبع غير صحيح',
        message: 'Tracking number not found'
      });
    }

    // Return limited information for public tracking
    const trackingInfo = {
      trackingNumber: shipment.trackingNumber,
      status: shipment.status,
      statusInArabic: getStatusInArabic(shipment.status),
      originCountry: shipment.originCountry,
      originCity: shipment.originCity,
      destinationCountry: shipment.destinationCountry,
      destinationCity: shipment.destinationCity,
      estimatedDelivery: shipment.estimatedDelivery,
      actualDelivery: shipment.actualDelivery,
      statusHistory: shipment.statusHistory.map(history => ({
        status: history.status,
        statusInArabic: getStatusInArabic(history.status),
        notes: history.notes,
        createdAt: history.createdAt
      }))
    };

    res.json({
      success: true,
      data: trackingInfo
    });

  } catch (error) {
    console.error('Track shipment error:', error);
    res.status(500).json({
      success: false,
      error: 'خطأ في الخادم',
      message: 'Internal server error'
    });
  }
});

// Helper function to get status in Arabic
function getStatusInArabic(status) {
  const statusMap = {
    'PREPARING': 'قيد التحضير',
    'SHIPPED': 'تم الشحن',
    'IN_TRANSIT': 'في الطريق',
    'CUSTOMS': 'في الجمارك',
    'OUT_FOR_DELIVERY': 'خرج للتوصيل',
    'DELIVERED': 'تم التسليم',
    'CANCELLED': 'ملغي',
    'RETURNED': 'مرتجع'
  };
  return statusMap[status] || status;
}

export default router;
