import { PrismaClient } from '@prisma/client';
import { hashPassword } from '../utils/auth.js';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create admin user
  const adminPassword = await hashPassword('admin123');
  const admin = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: adminPassword,
      role: 'ADMIN',
      isActive: true,
      admin: {
        create: {
          fullName: 'مدير النظام',
          phone: '+218-21-1234567'
        }
      }
    },
    include: {
      admin: true
    }
  });

  console.log('✅ Admin user created:', admin.email);

  // Create employee user
  const employeePassword = await hashPassword('employee123');
  const employee = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: employeePassword,
      role: 'EMPLOYEE',
      isActive: true,
      employee: {
        create: {
          fullName: 'أحمد محمد',
          phone: '+218-21-2345678',
          position: 'موظف شحن',
          department: 'العمليات'
        }
      }
    },
    include: {
      employee: true
    }
  });

  console.log('✅ Employee user created:', employee.email);

  // Create accountant user
  const accountantPassword = await hashPassword('accountant123');
  const accountant = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: accountantPassword,
      role: 'ACCOUNTANT',
      isActive: true,
      accountant: {
        create: {
          fullName: 'فاطمة علي',
          phone: '+218-21-3456789'
        }
      }
    },
    include: {
      accountant: true
    }
  });

  console.log('✅ Accountant user created:', accountant.email);

  // Create customer user
  const customerPassword = await hashPassword('customer123');
  const customer = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: customerPassword,
      role: 'CUSTOMER',
      isActive: true,
      customer: {
        create: {
          fullName: 'محمد أحمد الليبي',
          phone: '+218-91-1234567',
          address: 'شارع الجمهورية، طرابلس',
          city: 'طرابلس',
          country: 'Libya',
          companyName: 'شركة التجارة الليبية'
        }
      }
    },
    include: {
      customer: true
    }
  });

  console.log('✅ Customer user created:', customer.email);

  // Create company info
  const companyInfo = await prisma.companyInfo.create({
    data: {
      name: 'نظام إدارة الشحن',
      nameEn: 'Shipping Management System',
      email: '<EMAIL>',
      phone: '+218-21-1234567',
      address: 'شارع الجمهورية، طرابلس، ليبيا',
      city: 'طرابلس',
      country: 'ليبيا',
      website: 'https://shipping.com',
      description: 'نظام إدارة الشحن من الصين إلى ليبيا'
    }
  });

  console.log('✅ Company info created');

  // Create system settings
  const settings = [
    {
      key: 'notifications_enabled',
      value: 'true',
      description: 'تفعيل الإشعارات',
      category: 'notifications'
    },
    {
      key: 'auto_backup',
      value: 'true',
      description: 'النسخ الاحتياطي التلقائي',
      category: 'system'
    },
    {
      key: 'activity_logging',
      value: 'true',
      description: 'تسجيل الأنشطة',
      category: 'system'
    },
    {
      key: 'default_currency',
      value: 'USD',
      description: 'العملة الافتراضية',
      category: 'general'
    },
    {
      key: 'max_file_size',
      value: '5242880',
      description: 'الحد الأقصى لحجم الملف (بايت)',
      category: 'uploads'
    }
  ];

  for (const setting of settings) {
    await prisma.systemSetting.create({
      data: setting
    });
  }

  console.log('✅ System settings created');

  console.log('🎉 Database seeding completed successfully!');
  console.log('\n📋 Test Accounts:');
  console.log('👤 Admin: <EMAIL> / admin123');
  console.log('👤 Employee: <EMAIL> / employee123');
  console.log('👤 Accountant: <EMAIL> / accountant123');
  console.log('👤 Customer: <EMAIL> / customer123');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
