# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/shipping_db"

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="7d"

# Server Configuration
NODE_ENV="development"
PORT=3001

# File Upload Configuration
UPLOAD_PATH="./uploads"
MAX_FILE_SIZE=5242880  # 5MB in bytes

# Email Configuration (Optional - for future use)
SMTP_HOST=""
SMTP_PORT=587
SMTP_USER=""
SMTP_PASS=""
SMTP_FROM=""

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN="http://localhost:5173"

# Company Information
COMPANY_NAME="نظام إدارة الشحن"
COMPANY_NAME_EN="Shipping Management System"
COMPANY_EMAIL="<EMAIL>"
COMPANY_PHONE="+218-XX-XXXXXXX"
