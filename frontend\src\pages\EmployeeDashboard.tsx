import { Routes, Route } from 'react-router-dom'
import { UserGroupIcon, TruckIcon, PlusIcon, ChartBarIcon } from '@heroicons/react/24/outline'

const EmployeeDashboard = () => {
  return (
    <Routes>
      <Route path="/" element={<EmployeeHome />} />
      <Route path="/customers" element={<EmployeeCustomers />} />
      <Route path="/shipments" element={<EmployeeShipments />} />
      <Route path="/shipments/create" element={<CreateShipment />} />
    </Routes>
  )
}

const EmployeeHome = () => {
  const stats = [
    { name: 'إجمالي العملاء', value: '45', icon: UserGroupIcon, color: 'bg-blue-500' },
    { name: 'الشحنات اليوم', value: '8', icon: TruckIcon, color: 'bg-green-500' },
    { name: 'الشحنات النشطة', value: '23', icon: ChartBarIcon, color: 'bg-yellow-500' },
    { name: 'الشحنات المكتملة', value: '156', icon: TruckIcon, color: 'bg-purple-500' },
  ]

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">لوحة الموظف</h1>
        <p className="mt-1 text-sm text-gray-600">
          إدارة العملاء والشحنات
        </p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <div key={stat.name} className="card">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className={`${stat.color} rounded-md p-3`}>
                  <stat.icon className="h-6 w-6 text-white" />
                </div>
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    {stat.name}
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {stat.value}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
        <div className="card hover:shadow-md transition-shadow cursor-pointer">
          <div className="flex items-center p-6">
            <PlusIcon className="h-8 w-8 text-primary-600 ml-4" />
            <div>
              <h3 className="text-lg font-medium text-gray-900">إنشاء شحنة جديدة</h3>
              <p className="text-sm text-gray-500">إضافة شحنة جديدة للعميل</p>
            </div>
          </div>
        </div>

        <div className="card hover:shadow-md transition-shadow cursor-pointer">
          <div className="flex items-center p-6">
            <UserGroupIcon className="h-8 w-8 text-green-600 ml-4" />
            <div>
              <h3 className="text-lg font-medium text-gray-900">إضافة عميل جديد</h3>
              <p className="text-sm text-gray-500">تسجيل عميل جديد في النظام</p>
            </div>
          </div>
        </div>

        <div className="card hover:shadow-md transition-shadow cursor-pointer">
          <div className="flex items-center p-6">
            <TruckIcon className="h-8 w-8 text-yellow-600 ml-4" />
            <div>
              <h3 className="text-lg font-medium text-gray-900">تحديث حالة الشحنة</h3>
              <p className="text-sm text-gray-500">تحديث حالة الشحنات الحالية</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

const EmployeeCustomers = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">العملاء</h1>
          <p className="mt-1 text-sm text-gray-600">
            إدارة قائمة العملاء
          </p>
        </div>
        <button className="btn-primary">
          <PlusIcon className="h-5 w-5 ml-2" />
          إضافة عميل جديد
        </button>
      </div>

      <div className="card">
        <div className="px-4 py-5 sm:p-6">
          <div className="text-center py-8 text-gray-500">
            لا يوجد عملاء حالياً
          </div>
        </div>
      </div>
    </div>
  )
}

const EmployeeShipments = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">الشحنات</h1>
          <p className="mt-1 text-sm text-gray-600">
            إدارة جميع الشحنات
          </p>
        </div>
        <button className="btn-primary">
          <PlusIcon className="h-5 w-5 ml-2" />
          إنشاء شحنة جديدة
        </button>
      </div>

      <div className="card">
        <div className="px-4 py-5 sm:p-6">
          <div className="text-center py-8 text-gray-500">
            لا توجد شحنات حالياً
          </div>
        </div>
      </div>
    </div>
  )
}

const CreateShipment = () => {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">إنشاء شحنة جديدة</h1>
        <p className="mt-1 text-sm text-gray-600">
          إضافة شحنة جديدة للعميل
        </p>
      </div>

      <div className="card">
        <div className="px-4 py-5 sm:p-6">
          <form className="space-y-6">
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label className="form-label">العميل</label>
                <select className="form-input">
                  <option>اختر العميل</option>
                </select>
              </div>

              <div>
                <label className="form-label">رقم التتبع</label>
                <input type="text" className="form-input" placeholder="سيتم إنشاؤه تلقائياً" disabled />
              </div>

              <div>
                <label className="form-label">الوزن (كيلو)</label>
                <input type="number" className="form-input" placeholder="أدخل الوزن" />
              </div>

              <div>
                <label className="form-label">المبلغ الإجمالي</label>
                <input type="number" className="form-input" placeholder="أدخل المبلغ" />
              </div>
            </div>

            <div>
              <label className="form-label">وصف الشحنة</label>
              <textarea className="form-input" rows={3} placeholder="أدخل وصف الشحنة"></textarea>
            </div>

            <div className="flex justify-end gap-3">
              <button type="button" className="btn-secondary">
                إلغاء
              </button>
              <button type="submit" className="btn-primary">
                إنشاء الشحنة
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

export default EmployeeDashboard
