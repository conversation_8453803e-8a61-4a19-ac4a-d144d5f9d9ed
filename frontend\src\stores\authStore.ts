import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { User, Customer, Employee, Accountant, Admin } from '../types'

interface AuthState {
  user: User | null
  profile: Customer | Employee | Accountant | Admin | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

interface AuthActions {
  login: (email: string, password: string) => Promise<void>
  logout: () => void
  setUser: (user: User, profile: Customer | Employee | Accountant | Admin, token: string) => void
  clearError: () => void
  setLoading: (loading: boolean) => void
}

type AuthStore = AuthState & AuthActions

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // State
      user: null,
      profile: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      login: async (email: string, password: string) => {
        try {
          set({ isLoading: true, error: null })

          const response = await fetch(`${import.meta.env.VITE_API_URL}/api/auth/login`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email, password }),
          })

          const data = await response.json()

          if (!response.ok) {
            throw new Error(data.message || 'فشل في تسجيل الدخول')
          }

          const { user, profile, token } = data.data

          set({
            user,
            profile,
            token,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          })

          // Set token in axios defaults
          if (typeof window !== 'undefined') {
            localStorage.setItem('token', token)
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'حدث خطأ غير متوقع',
            isLoading: false,
            isAuthenticated: false,
            user: null,
            profile: null,
            token: null,
          })
          throw error
        }
      },

      logout: () => {
        set({
          user: null,
          profile: null,
          token: null,
          isAuthenticated: false,
          error: null,
        })

        // Remove token from localStorage
        if (typeof window !== 'undefined') {
          localStorage.removeItem('token')
        }
      },

      setUser: (user: User, profile: Customer | Employee | Accountant | Admin, token: string) => {
        set({
          user,
          profile,
          token,
          isAuthenticated: true,
          error: null,
        })
      },

      clearError: () => {
        set({ error: null })
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        profile: state.profile,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
)
