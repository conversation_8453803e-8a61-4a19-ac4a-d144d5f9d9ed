import { Routes, Route, Navigate } from 'react-router-dom'
import { useAuthStore } from './stores/authStore'
import LoginPage from './pages/LoginPage'
import CustomerDashboard from './pages/CustomerDashboard'
import EmployeeDashboard from './pages/EmployeeDashboard'
import AccountantDashboard from './pages/AccountantDashboard'
import AdminDashboard from './pages/AdminDashboard'
import Layout from './components/Layout'
import ProtectedRoute from './components/ProtectedRoute'

function App() {
  const { user, isAuthenticated } = useAuthStore()

  if (!isAuthenticated) {
    return <LoginPage />
  }

  const getDashboardRoute = () => {
    switch (user?.role) {
      case 'CUSTOMER':
        return '/customer'
      case 'EMPLOYEE':
        return '/employee'
      case 'ACCOUNTANT':
        return '/accountant'
      case 'ADMIN':
        return '/admin'
      default:
        return '/login'
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 rtl">
      <Routes>
        <Route path="/login" element={<LoginPage />} />
        
        <Route path="/" element={<Navigate to={getDashboardRoute()} replace />} />
        
        <Route path="/customer/*" element={
          <ProtectedRoute allowedRoles={['CUSTOMER']}>
            <Layout>
              <CustomerDashboard />
            </Layout>
          </ProtectedRoute>
        } />
        
        <Route path="/employee/*" element={
          <ProtectedRoute allowedRoles={['EMPLOYEE']}>
            <Layout>
              <EmployeeDashboard />
            </Layout>
          </ProtectedRoute>
        } />
        
        <Route path="/accountant/*" element={
          <ProtectedRoute allowedRoles={['ACCOUNTANT']}>
            <Layout>
              <AccountantDashboard />
            </Layout>
          </ProtectedRoute>
        } />
        
        <Route path="/admin/*" element={
          <ProtectedRoute allowedRoles={['ADMIN']}>
            <Layout>
              <AdminDashboard />
            </Layout>
          </ProtectedRoute>
        } />
        
        <Route path="*" element={<Navigate to={getDashboardRoute()} replace />} />
      </Routes>
    </div>
  )
}

export default App
