// نظام إدارة الشحن - الصين إلى ليبيا
// Shipping Management System - China to Libya
// Database Schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

// نموذج المستخدمين - Users Model
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  role      UserRole @default(CUSTOMER)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // العلاقات - Relations
  customer      Customer?
  employee      Employee?
  accountant    Accountant?
  admin         Admin?
  notifications Notification[]

  @@map("users")
}

// أدوار المستخدمين - User Roles
enum UserRole {
  CUSTOMER   // عميل
  EMPLOYEE   // موظف
  ACCOUNTANT // محاسب
  ADMIN      // مدير النظام
}

// نموذج العملاء - Customers Model
model Customer {
  id          String   @id @default(cuid())
  userId      String   @unique
  fullName    String
  phone       String
  address     String?
  city        String?
  country     String   @default("Libya")
  companyName String?
  taxNumber   String?
  notes       String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // العلاقات - Relations
  user      User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  shipments Shipment[]

  @@map("customers")
}

// نموذج الموظفين - Employees Model
model Employee {
  id          String   @id @default(cuid())
  userId      String   @unique
  fullName    String
  phone       String
  position    String
  department  String?
  hireDate    DateTime @default(now())
  salary      Decimal?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // العلاقات - Relations
  user              User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  createdShipments  Shipment[] @relation("EmployeeCreatedShipments")
  updatedShipments  Shipment[] @relation("EmployeeUpdatedShipments")

  @@map("employees")
}

// نموذج المحاسبين - Accountants Model
model Accountant {
  id        String   @id @default(cuid())
  userId    String   @unique
  fullName  String
  phone     String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // العلاقات - Relations
  user     User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  payments Payment[]

  @@map("accountants")
}

// نموذج المديرين - Admins Model
model Admin {
  id        String   @id @default(cuid())
  userId    String   @unique
  fullName  String
  phone     String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // العلاقات - Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("admins")
}

// نموذج الشحنات - Shipments Model
model Shipment {
  id              String         @id @default(cuid())
  trackingNumber  String         @unique
  customerId      String
  originCountry   String         @default("China")
  originCity      String?
  destinationCountry String      @default("Libya")
  destinationCity String?
  weight          Decimal
  dimensions      String?        // الأبعاد
  description     String
  status          ShipmentStatus @default(PREPARING)
  totalAmount     Decimal
  paidAmount      Decimal        @default(0)
  remainingAmount Decimal        @default(0)
  currency        String         @default("USD")
  estimatedDelivery DateTime?
  actualDelivery  DateTime?
  notes           String?
  createdById     String?        // الموظف الذي أنشأ الشحنة
  updatedById     String?        // آخر موظف قام بالتحديث
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt

  // العلاقات - Relations
  customer        Customer       @relation(fields: [customerId], references: [id])
  createdBy       Employee?      @relation("EmployeeCreatedShipments", fields: [createdById], references: [id])
  updatedBy       Employee?      @relation("EmployeeUpdatedShipments", fields: [updatedById], references: [id])
  payments        Payment[]
  statusHistory   ShipmentStatusHistory[]
  documents       ShipmentDocument[]

  @@map("shipments")
}

// حالات الشحنة - Shipment Status
enum ShipmentStatus {
  PREPARING     // قيد التحضير
  SHIPPED       // تم الشحن
  IN_TRANSIT    // في الطريق
  CUSTOMS       // في الجمارك
  OUT_FOR_DELIVERY // خرج للتوصيل
  DELIVERED     // تم التسليم
  CANCELLED     // ملغي
  RETURNED      // مرتجع
}

// تاريخ حالات الشحنة - Shipment Status History
model ShipmentStatusHistory {
  id         String         @id @default(cuid())
  shipmentId String
  status     ShipmentStatus
  notes      String?
  createdAt  DateTime       @default(now())

  // العلاقات - Relations
  shipment Shipment @relation(fields: [shipmentId], references: [id], onDelete: Cascade)

  @@map("shipment_status_history")
}

// مستندات الشحنة - Shipment Documents
model ShipmentDocument {
  id         String      @id @default(cuid())
  shipmentId String
  fileName   String
  filePath   String
  fileSize   Int
  fileType   String
  uploadedAt DateTime    @default(now())

  // العلاقات - Relations
  shipment Shipment @relation(fields: [shipmentId], references: [id], onDelete: Cascade)

  @@map("shipment_documents")
}

// نموذج المدفوعات - Payments Model
model Payment {
  id            String        @id @default(cuid())
  shipmentId    String
  amount        Decimal
  currency      String        @default("USD")
  paymentMethod PaymentMethod
  paymentDate   DateTime      @default(now())
  receiptNumber String?
  receiptFile   String?       // مسار ملف الإيصال
  notes         String?
  processedById String?       // المحاسب الذي سجل الدفعة
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // العلاقات - Relations
  shipment    Shipment    @relation(fields: [shipmentId], references: [id])
  processedBy Accountant? @relation(fields: [processedById], references: [id])

  @@map("payments")
}

// طرق الدفع - Payment Methods
enum PaymentMethod {
  CASH          // نقدي
  BANK_TRANSFER // تحويل بنكي
  CREDIT_CARD   // بطاقة ائتمان
  CHECK         // شيك
  ONLINE        // دفع إلكتروني
  OTHER         // أخرى
}

// إعدادات النظام - System Settings
model SystemSetting {
  id          String   @id @default(cuid())
  key         String   @unique
  value       String
  description String?
  category    String   @default("general")
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("system_settings")
}

// معلومات الشركة - Company Information
model CompanyInfo {
  id          String   @id @default(cuid())
  name        String
  nameEn      String?
  logo        String?
  email       String
  phone       String
  address     String
  city        String
  country     String
  website     String?
  taxNumber   String?
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("company_info")
}

// الإشعارات - Notifications
model Notification {
  id        String           @id @default(cuid())
  userId    String
  title     String
  message   String
  type      NotificationType @default(INFO)
  isRead    Boolean          @default(false)
  createdAt DateTime         @default(now())

  // العلاقات - Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

// أنواع الإشعارات - Notification Types
enum NotificationType {
  INFO      // معلومات
  SUCCESS   // نجاح
  WARNING   // تحذير
  ERROR     // خطأ
  SHIPMENT  // شحنة
  PAYMENT   // دفعة
}

// سجل النشاطات - Activity Log
model ActivityLog {
  id          String   @id @default(cuid())
  userId      String?
  action      String
  description String
  ipAddress   String?
  userAgent   String?
  createdAt   DateTime @default(now())

  @@map("activity_logs")
}
