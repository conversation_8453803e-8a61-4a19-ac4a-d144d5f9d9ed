import jwt from 'jsonwebtoken';
import prisma from '../config/database.js';

// Verify JWT token
export const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        success: false,
        error: 'رمز المصادقة مطلوب',
        message: 'Access token is required'
      });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Get user from database
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      include: {
        customer: true,
        employee: true,
        accountant: true,
        admin: true
      }
    });

    if (!user || !user.isActive) {
      return res.status(401).json({
        success: false,
        error: 'المستخدم غير موجود أو غير نشط',
        message: 'User not found or inactive'
      });
    }

    req.user = user;
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        error: 'رمز المصادقة غير صحيح',
        message: 'Invalid token'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        error: 'انتهت صلاحية رمز المصادقة',
        message: 'Token expired'
      });
    }

    console.error('Auth middleware error:', error);
    res.status(500).json({
      success: false,
      error: 'خطأ في المصادقة',
      message: 'Authentication error'
    });
  }
};

// Check user role
export const requireRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'المصادقة مطلوبة',
        message: 'Authentication required'
      });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        error: 'ليس لديك صلاحية للوصول إلى هذا المورد',
        message: 'Insufficient permissions'
      });
    }

    next();
  };
};

// Check if user owns resource or has admin access
export const requireOwnershipOrRole = (roles = []) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'المصادقة مطلوبة',
        message: 'Authentication required'
      });
    }

    // Allow if user has required role
    if (roles.includes(req.user.role)) {
      return next();
    }

    // Allow if user is accessing their own resource
    const resourceUserId = req.params.userId || req.body.userId;
    if (resourceUserId && resourceUserId === req.user.id) {
      return next();
    }

    return res.status(403).json({
      success: false,
      error: 'ليس لديك صلاحية للوصول إلى هذا المورد',
      message: 'Insufficient permissions'
    });
  };
};
