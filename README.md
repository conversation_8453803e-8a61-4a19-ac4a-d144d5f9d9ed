# نظام إدارة الشحن - الصين إلى ليبيا
## Shipping Management System - China to Libya

نظام إدارة شحن شامل للشركات العاملة في مجال الشحن من الصين إلى ليبيا، مع واجهة عربية حديثة ونظام إدارة متقدم.

### 🚀 المميزات الرئيسية

- **واجهة عربية كاملة** مع دعم RTL
- **4 لوحات تحكم منفصلة** حسب صلاحيات المستخدم
- **نظام تتبع الشحنات** المتقدم
- **إدارة المدفوعات** والتقارير المالية
- **تصميم متجاوب** للهاتف والحاسوب
- **جاهز للنشر** على خادم VPS

### 🏗️ هيكل المشروع

```
shipping-app/
├── frontend/          # تطبيق React
├── backend/           # API Express.js
├── database/          # قاعدة البيانات والمخططات
├── docs/             # الوثائق
├── scripts/          # سكريبت النشر
└── README.md         # تعليمات الإعداد
```

### 🛠️ التقنيات المستخدمة

#### Frontend:
- React.js with Vite
- Tailwind CSS with RTL
- React Router
- Axios
- React Hook Form
- TanStack Query

#### Backend:
- Node.js with Express.js
- PostgreSQL
- Prisma ORM
- JWT Authentication
- Bcrypt
- Multer

### 📋 لوحات التحكم

#### 🔵 لوحة العميل
- عرض الشحنات الخاصة
- تتبع الشحنات
- رفع إيصالات الدفع
- عرض تاريخ المدفوعات

#### 🟢 لوحة الموظف
- إدارة العملاء
- إنشاء شحنات جديدة
- تحديث حالة الشحنات
- الإحصائيات الأساسية

#### 🟡 لوحة المحاسب
- تسجيل المدفوعات
- التقارير المالية
- تصدير التقارير (PDF/Excel)
- متابعة المدفوعات

#### 🔴 لوحة المدير
- إدارة المستخدمين
- إعدادات النظام
- النسخ الاحتياطي
- صيانة النظام

### 🚀 تعليمات التشغيل

#### التطوير المحلي:

1. **استنساخ المشروع:**
```bash
git clone <repository-url>
cd shipping-app
```

2. **إعداد قاعدة البيانات:**
```bash
cd database
# إنشاء قاعدة البيانات
createdb shipping_db
# تشغيل المخططات
psql -d shipping_db -f schema.sql
```

3. **إعداد Backend:**
```bash
cd backend
npm install
cp .env.example .env
# تحديث متغيرات البيئة
npm run dev
```

4. **إعداد Frontend:**
```bash
cd frontend
npm install
npm run dev
```

### 🌐 النشر على VPS

#### متطلبات الخادم:
- Ubuntu 20.04+ 
- Node.js 18+
- PostgreSQL 14+
- Nginx
- PM2

#### خطوات النشر:

1. **رفع الملفات:**
```bash
scp -r shipping-app/ user@server:/var/www/
```

2. **إعداد قاعدة البيانات:**
```bash
sudo -u postgres createdb shipping_db
sudo -u postgres psql -d shipping_db -f database/schema.sql
```

3. **إعداد Backend:**
```bash
cd /var/www/shipping-app/backend
npm install --production
cp .env.example .env
# تحديث متغيرات الإنتاج
pm2 start ecosystem.config.js
```

4. **بناء Frontend:**
```bash
cd /var/www/shipping-app/frontend
npm install
npm run build
```

5. **إعداد Nginx:**
```bash
sudo cp scripts/nginx.conf /etc/nginx/sites-available/shipping-app
sudo ln -s /etc/nginx/sites-available/shipping-app /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 🔧 متغيرات البيئة

#### Backend (.env):
```
NODE_ENV=production
PORT=3001
DATABASE_URL=postgresql://username:password@localhost:5432/shipping_db
JWT_SECRET=your-super-secret-jwt-key
UPLOAD_PATH=/var/www/shipping-app/uploads
```

#### Frontend (.env):
```
VITE_API_URL=https://yourdomain.com/api
VITE_APP_NAME=نظام إدارة الشحن
```

### 📚 الوثائق

- [دليل المستخدم](docs/user-guide.md)
- [وثائق API](docs/api-documentation.md)
- [دليل النشر](docs/deployment-guide.md)
- [استكشاف الأخطاء](docs/troubleshooting.md)

### 🔒 الأمان

- تشفير كلمات المرور باستخدام bcrypt
- مصادقة JWT آمنة
- حماية CORS
- تحقق من صحة البيانات
- رفع آمن للملفات

### 📞 الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى إنشاء issue في المستودع.

### 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT.
