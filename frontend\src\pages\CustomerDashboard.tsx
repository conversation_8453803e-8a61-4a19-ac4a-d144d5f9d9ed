import { useState } from 'react'
import { Routes, Route } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import { TruckIcon, CreditCardIcon, ClockIcon, CheckCircleIcon, DocumentArrowUpIcon } from '@heroicons/react/24/outline'
import { dashboardAPI, shipmentsAPI, paymentsAPI } from '../services/api'
import LoadingSpinner from '../components/LoadingSpinner'
import ErrorMessage from '../components/ErrorMessage'
import { Shipment, Payment } from '../types'

const CustomerDashboard = () => {
  return (
    <Routes>
      <Route path="/" element={<CustomerHome />} />
      <Route path="/shipments" element={<CustomerShipments />} />
      <Route path="/payments" element={<CustomerPayments />} />
      <Route path="/tracking" element={<CustomerTracking />} />
    </Routes>
  )
}

const CustomerHome = () => {
  const { data: dashboardData, isLoading, error, refetch } = useQuery({
    queryKey: ['dashboard-stats'],
    queryFn: async () => {
      const response = await dashboardAPI.getStats()
      return response.data.data
    }
  })

  if (isLoading) {
    return <LoadingSpinner text="جاري تحميل البيانات..." />
  }

  if (error) {
    return <ErrorMessage message="حدث خطأ في تحميل البيانات" onRetry={refetch} />
  }

  const stats = [
    {
      name: 'إجمالي الشحنات',
      value: dashboardData?.totalShipments?.toString() || '0',
      icon: TruckIcon,
      color: 'bg-blue-500'
    },
    {
      name: 'الشحنات النشطة',
      value: dashboardData?.activeShipments?.toString() || '0',
      icon: ClockIcon,
      color: 'bg-yellow-500'
    },
    {
      name: 'الشحنات المكتملة',
      value: dashboardData?.deliveredShipments?.toString() || '0',
      icon: CheckCircleIcon,
      color: 'bg-green-500'
    },
    {
      name: 'المدفوعات المعلقة',
      value: dashboardData?.pendingPayments?.toString() || '0',
      icon: CreditCardIcon,
      color: 'bg-red-500'
    },
  ]

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">مرحباً بك في لوحة العميل</h1>
        <p className="mt-1 text-sm text-gray-600">
          تابع شحناتك ومدفوعاتك من هنا
        </p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <div key={stat.name} className="card">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className={`${stat.color} rounded-md p-3`}>
                  <stat.icon className="h-6 w-6 text-white" />
                </div>
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    {stat.name}
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {stat.value}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Recent Shipments */}
      <div className="card">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            الشحنات الأخيرة
          </h3>
          {dashboardData?.recentShipments?.length > 0 ? (
            <div className="space-y-3">
              {dashboardData.recentShipments.map((shipment: Shipment) => (
                <div key={shipment.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium text-gray-900">{shipment.trackingNumber}</p>
                    <p className="text-sm text-gray-500">{shipment.description}</p>
                  </div>
                  <div className="text-left">
                    <p className="text-sm font-medium text-gray-900">
                      ${shipment.totalAmount}
                    </p>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      shipment.status === 'DELIVERED' ? 'bg-green-100 text-green-800' :
                      shipment.status === 'IN_TRANSIT' ? 'bg-blue-100 text-blue-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {getStatusInArabic(shipment.status)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              لا توجد شحنات حالياً
            </div>
          )}
        </div>
      </div>

      {/* Recent Payments */}
      <div className="card">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            المدفوعات الأخيرة
          </h3>
          {dashboardData?.recentPayments?.length > 0 ? (
            <div className="space-y-3">
              {dashboardData.recentPayments.map((payment: Payment) => (
                <div key={payment.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium text-gray-900">
                      {payment.shipment?.trackingNumber}
                    </p>
                    <p className="text-sm text-gray-500">
                      {new Date(payment.paymentDate).toLocaleDateString('ar-SA')}
                    </p>
                  </div>
                  <div className="text-left">
                    <p className="text-sm font-medium text-gray-900">
                      ${payment.amount} {payment.currency}
                    </p>
                    <p className="text-xs text-gray-500">
                      {getPaymentMethodInArabic(payment.paymentMethod)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              لا توجد مدفوعات حالياً
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

const CustomerShipments = () => {
  const { data: shipmentsData, isLoading, error, refetch } = useQuery({
    queryKey: ['customer-shipments'],
    queryFn: async () => {
      const response = await shipmentsAPI.getAll()
      return response.data.data
    }
  })

  if (isLoading) {
    return <LoadingSpinner text="جاري تحميل الشحنات..." />
  }

  if (error) {
    return <ErrorMessage message="حدث خطأ في تحميل الشحنات" onRetry={refetch} />
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">شحناتي</h1>
        <p className="mt-1 text-sm text-gray-600">
          عرض جميع الشحنات الخاصة بك
        </p>
      </div>

      <div className="card">
        <div className="px-4 py-5 sm:p-6">
          {shipmentsData?.shipments?.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      رقم التتبع
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الوصف
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الحالة
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      المبلغ الإجمالي
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      المبلغ المتبقي
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      تاريخ الإنشاء
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {shipmentsData.shipments.map((shipment: Shipment) => (
                    <tr key={shipment.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {shipment.trackingNumber}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {shipment.description}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          shipment.status === 'DELIVERED' ? 'bg-green-100 text-green-800' :
                          shipment.status === 'IN_TRANSIT' ? 'bg-blue-100 text-blue-800' :
                          shipment.status === 'CANCELLED' ? 'bg-red-100 text-red-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {getStatusInArabic(shipment.status)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${shipment.totalAmount}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${shipment.remainingAmount}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(shipment.createdAt).toLocaleDateString('ar-SA')}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              لا توجد شحنات حالياً
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

const CustomerPayments = () => {
  const { data: paymentsData, isLoading, error, refetch } = useQuery({
    queryKey: ['customer-payments'],
    queryFn: async () => {
      const response = await paymentsAPI.getAll()
      return response.data.data
    }
  })

  if (isLoading) {
    return <LoadingSpinner text="جاري تحميل المدفوعات..." />
  }

  if (error) {
    return <ErrorMessage message="حدث خطأ في تحميل المدفوعات" onRetry={refetch} />
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">المدفوعات</h1>
        <p className="mt-1 text-sm text-gray-600">
          عرض تاريخ المدفوعات ورفع الإيصالات
        </p>
      </div>

      <div className="card">
        <div className="px-4 py-5 sm:p-6">
          {paymentsData?.payments?.length > 0 ? (
            <div className="space-y-4">
              {paymentsData.payments.map((payment: Payment) => (
                <div key={payment.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <h4 className="text-lg font-medium text-gray-900">
                        الشحنة: {payment.shipment?.trackingNumber}
                      </h4>
                      <p className="text-sm text-gray-500">
                        تاريخ الدفع: {new Date(payment.paymentDate).toLocaleDateString('ar-SA')}
                      </p>
                    </div>
                    <div className="text-left">
                      <p className="text-lg font-semibold text-gray-900">
                        ${payment.amount} {payment.currency}
                      </p>
                      <p className="text-sm text-gray-500">
                        {getPaymentMethodInArabic(payment.paymentMethod)}
                      </p>
                    </div>
                  </div>

                  {payment.receiptFile ? (
                    <div className="flex items-center text-green-600">
                      <CheckCircleIcon className="h-5 w-5 ml-2" />
                      <span className="text-sm">تم رفع الإيصال</span>
                    </div>
                  ) : (
                    <div className="flex items-center justify-between">
                      <div className="flex items-center text-yellow-600">
                        <ClockIcon className="h-5 w-5 ml-2" />
                        <span className="text-sm">لم يتم رفع الإيصال</span>
                      </div>
                      <button className="btn-primary flex items-center">
                        <DocumentArrowUpIcon className="h-4 w-4 ml-2" />
                        رفع الإيصال
                      </button>
                    </div>
                  )}

                  {payment.notes && (
                    <div className="mt-3 p-3 bg-gray-50 rounded-md">
                      <p className="text-sm text-gray-600">{payment.notes}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              لا توجد مدفوعات حالياً
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

const CustomerTracking = () => {
  const [trackingNumber, setTrackingNumber] = useState('')
  const [trackingData, setTrackingData] = useState(null)
  const [isTracking, setIsTracking] = useState(false)
  const [trackingError, setTrackingError] = useState('')

  const handleTrack = async () => {
    if (!trackingNumber.trim()) {
      setTrackingError('يرجى إدخال رقم التتبع')
      return
    }

    setIsTracking(true)
    setTrackingError('')
    setTrackingData(null)

    try {
      const response = await shipmentsAPI.track(trackingNumber.trim())
      setTrackingData(response.data.data)
    } catch (error: any) {
      setTrackingError(error.response?.data?.error || 'حدث خطأ في تتبع الشحنة')
    } finally {
      setIsTracking(false)
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">تتبع الشحنة</h1>
        <p className="mt-1 text-sm text-gray-600">
          تتبع شحنتك باستخدام رقم التتبع
        </p>
      </div>

      <div className="card">
        <div className="px-4 py-5 sm:p-6">
          <div className="max-w-md">
            <label htmlFor="tracking" className="form-label">
              رقم التتبع
            </label>
            <div className="flex gap-3">
              <input
                type="text"
                id="tracking"
                value={trackingNumber}
                onChange={(e) => setTrackingNumber(e.target.value)}
                className="form-input flex-1"
                placeholder="أدخل رقم التتبع"
                onKeyPress={(e) => e.key === 'Enter' && handleTrack()}
              />
              <button
                onClick={handleTrack}
                disabled={isTracking}
                className="btn-primary disabled:opacity-50"
              >
                {isTracking ? 'جاري التتبع...' : 'تتبع'}
              </button>
            </div>
            {trackingError && (
              <p className="mt-2 text-sm text-red-600">{trackingError}</p>
            )}
          </div>
        </div>
      </div>

      {trackingData && (
        <div className="card">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              معلومات الشحنة: {trackingData.trackingNumber}
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div>
                <p className="text-sm font-medium text-gray-500">الحالة الحالية</p>
                <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${
                  trackingData.status === 'DELIVERED' ? 'bg-green-100 text-green-800' :
                  trackingData.status === 'IN_TRANSIT' ? 'bg-blue-100 text-blue-800' :
                  'bg-yellow-100 text-yellow-800'
                }`}>
                  {trackingData.statusInArabic}
                </span>
              </div>

              <div>
                <p className="text-sm font-medium text-gray-500">من</p>
                <p className="text-sm text-gray-900">
                  {trackingData.originCity}, {trackingData.originCountry}
                </p>
              </div>

              <div>
                <p className="text-sm font-medium text-gray-500">إلى</p>
                <p className="text-sm text-gray-900">
                  {trackingData.destinationCity}, {trackingData.destinationCountry}
                </p>
              </div>

              {trackingData.estimatedDelivery && (
                <div>
                  <p className="text-sm font-medium text-gray-500">التسليم المتوقع</p>
                  <p className="text-sm text-gray-900">
                    {new Date(trackingData.estimatedDelivery).toLocaleDateString('ar-SA')}
                  </p>
                </div>
              )}
            </div>

            {trackingData.statusHistory && trackingData.statusHistory.length > 0 && (
              <div>
                <h4 className="text-md font-medium text-gray-900 mb-3">تاريخ الشحنة</h4>
                <div className="space-y-3">
                  {trackingData.statusHistory.map((history: any, index: number) => (
                    <div key={index} className="flex items-start">
                      <div className="flex-shrink-0">
                        <div className={`w-3 h-3 rounded-full ${
                          index === 0 ? 'bg-primary-600' : 'bg-gray-300'
                        }`}></div>
                      </div>
                      <div className="mr-3 flex-1">
                        <p className="text-sm font-medium text-gray-900">
                          {history.statusInArabic}
                        </p>
                        <p className="text-xs text-gray-500">
                          {new Date(history.createdAt).toLocaleString('ar-SA')}
                        </p>
                        {history.notes && (
                          <p className="text-xs text-gray-600 mt-1">{history.notes}</p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

// Helper functions
function getStatusInArabic(status: string) {
  const statusMap: Record<string, string> = {
    'PREPARING': 'قيد التحضير',
    'SHIPPED': 'تم الشحن',
    'IN_TRANSIT': 'في الطريق',
    'CUSTOMS': 'في الجمارك',
    'OUT_FOR_DELIVERY': 'خرج للتوصيل',
    'DELIVERED': 'تم التسليم',
    'CANCELLED': 'ملغي',
    'RETURNED': 'مرتجع'
  }
  return statusMap[status] || status
}

function getPaymentMethodInArabic(method: string) {
  const methodMap: Record<string, string> = {
    'CASH': 'نقدي',
    'BANK_TRANSFER': 'تحويل بنكي',
    'CREDIT_CARD': 'بطاقة ائتمان',
    'CHECK': 'شيك',
    'ONLINE': 'دفع إلكتروني',
    'OTHER': 'أخرى'
  }
  return methodMap[method] || method
}

export default CustomerDashboard
